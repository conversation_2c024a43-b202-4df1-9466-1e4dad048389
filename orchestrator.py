from openai import OpenAI
import tools.math_tools as math_tools

# Connect to local LM Studio API
client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")

# Tool registry mapping names to actual Python functions
TOOL_REGISTRY = {
    "add_numbers": math_tools.add_numbers,
    "sub_numbers": math_tools.sub_numbers,
    "multiply_numbers": math_tools.multiply_numbers,
    "divide_numbers": math_tools.divide_numbers,
    "square_number": math_tools.square_number,
    "cube_number": math_tools.cube_number,
    "square_root": math_tools.square_root,
    "cube_root": math_tools.cube_root
}

# System instructions (just names, no JSON)
system_msg = {
    "role": "system",
    "content": "You are a helpful assistant. "
               "When a math operation is needed, reply ONLY with:\n"
               "TOOL:<tool_name> ARGS:<comma-separated arguments>"
}

while True:
    user_input = input("User: ")
    user_msg = {"role": "user", "content": user_input}

    resp = client.chat.completions.create(
        model="qwen-4b",
        messages=[system_msg, user_msg]
    )

    reply = resp.choices[0].message["content"].strip()

    if reply.startswith("TOOL:"):
        try:
            _, tool_name_part = reply.split("TOOL:", 1)
            tool_name, args_part = tool_name_part.strip().split("ARGS:")
            tool_name = tool_name.strip()
            args = [float(a.strip()) for a in args_part.split(",")]

            if tool_name in TOOL_REGISTRY:
                result = TOOL_REGISTRY[tool_name](*args)
                print(f"Result: {result}")
            else:
                print(f"Unknown tool: {tool_name}")

        except Exception as e:
            print(f"Error parsing tool call: {e}")
    else:
        print("Assistant:", reply)